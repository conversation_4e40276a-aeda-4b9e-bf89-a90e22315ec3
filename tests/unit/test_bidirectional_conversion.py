"""Test bidirectional format conversion between OpenAI and Anthropic APIs."""
import json
from typing import Any, Dict, List

import pytest

from llm_grok.formats.openai import OpenAIFormatHandler
from llm_grok.formats.anthropic import Anthropic<PERSON>ormatHandler
from llm_grok.exceptions import ConversionError


class TestBidirectionalConversion:
    """Test converting between OpenAI and Anthropic formats preserves data."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.openai_handler = OpenAIFormatHandler(model_id="test-model")
        self.anthropic_handler = AnthropicFormatHandler(model_id="test-model")
    
    def _convert_openai_to_anthropic_and_back(self, openai_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert OpenAI → Anthropic → OpenAI and return result."""
        # Convert to Anthropic format
        anthropic_data = self.anthropic_handler.convert_from_openai(openai_data)
        
        # Convert back to OpenAI format
        openai_result = self.openai_handler.convert_from_anthropic(anthropic_data)
        
        return openai_result
    
    def _convert_anthropic_to_openai_and_back(self, anthropic_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Anthropic → OpenAI → Anthropic and return result."""
        # Convert to OpenAI format
        openai_data = self.openai_handler.convert_from_anthropic(anthropic_data)
        
        # Convert back to Anthropic format
        anthropic_result = self.anthropic_handler.convert_from_openai(openai_data)
        
        return anthropic_result
    
    def test_simple_text_message_bidirectional(self) -> None:
        """Test simple text messages convert correctly both ways."""
        # OpenAI → Anthropic → OpenAI
        openai_msg = {
            "messages": [
                {"role": "system", "content": "You are helpful."},
                {"role": "user", "content": "Hello"},
                {"role": "assistant", "content": "Hi there!"}
            ]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # Should preserve all messages
        assert len(result["messages"]) == 3
        assert result["messages"][0]["role"] == "system"
        assert result["messages"][0]["content"] == "You are helpful."
        assert result["messages"][1]["role"] == "user"
        assert result["messages"][1]["content"] == "Hello"
        assert result["messages"][2]["role"] == "assistant"
        assert result["messages"][2]["content"] == "Hi there!"
    
    def test_multimodal_content_bidirectional(self) -> None:
        """Test multimodal messages with images convert correctly."""
        # OpenAI format with image
        openai_msg = {
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": "What's in this image?"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # Should preserve multimodal structure
        assert len(result["messages"]) == 1
        assert result["messages"][0]["role"] == "user"
        assert isinstance(result["messages"][0]["content"], list)
        assert len(result["messages"][0]["content"]) == 2
        
        # Check text part
        assert result["messages"][0]["content"][0]["type"] == "text"
        assert result["messages"][0]["content"][0]["text"] == "What's in this image?"
        
        # Check image part
        assert result["messages"][0]["content"][1]["type"] == "image_url"
        assert result["messages"][0]["content"][1]["image_url"]["url"] == "https://example.com/image.jpg"
    
    def test_tool_calls_bidirectional(self) -> None:
        """Test tool calls convert correctly both ways."""
        # OpenAI format with tool calls
        openai_msg = {
            "messages": [{
                "role": "assistant",
                "content": "I'll search for that.",
                "tool_calls": [{
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "search",
                        "arguments": '{"query": "weather in NYC"}'
                    }
                }]
            }],
            "tools": [{
                "type": "function",
                "function": {
                    "name": "search",
                    "description": "Search the web",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string"}
                        },
                        "required": ["query"]
                    }
                }
            }]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # Should preserve tool calls
        assert len(result["messages"]) == 1
        assert result["messages"][0]["role"] == "assistant"
        assert result["messages"][0]["content"] == "I'll search for that."
        assert "tool_calls" in result["messages"][0]
        assert len(result["messages"][0]["tool_calls"]) == 1
        
        tool_call = result["messages"][0]["tool_calls"][0]
        assert tool_call["id"] == "call_123"
        assert tool_call["type"] == "function"
        assert tool_call["function"]["name"] == "search"
        assert json.loads(tool_call["function"]["arguments"]) == {"query": "weather in NYC"}
        
        # Should preserve tool definitions
        assert "tools" in result
        assert len(result["tools"]) == 1
        assert result["tools"][0]["function"]["name"] == "search"
    
    def test_complex_conversation_bidirectional(self) -> None:
        """Test complex multi-turn conversation with various features."""
        openai_conversation = {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant with access to tools."},
                {"role": "user", "content": "What's the weather like?"},
                {
                    "role": "assistant",
                    "content": "I'll check the weather for you.",
                    "tool_calls": [{
                        "id": "call_weather",
                        "type": "function",
                        "function": {
                            "name": "get_weather",
                            "arguments": '{"location": "current"}'
                        }
                    }]
                },
                {
                    "role": "tool",
                    "tool_call_id": "call_weather",
                    "content": '{"temperature": 72, "condition": "sunny"}'
                },
                {
                    "role": "assistant",
                    "content": "The weather is currently 72°F and sunny."
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Here's a photo from outside:"},
                        {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,/9j/4AAQ..."}}
                    ]
                },
                {
                    "role": "assistant",
                    "content": "I can see it's indeed a beautiful sunny day in the photo!"
                }
            ],
            "tools": [{
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get current weather",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string"}
                        }
                    }
                }
            }]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_conversation)
        
        # Should preserve all messages
        assert len(result["messages"]) == len(openai_conversation["messages"])
        
        # Check system message
        assert result["messages"][0]["role"] == "system"
        assert result["messages"][0]["content"] == "You are a helpful assistant with access to tools."
        
        # Check tool call message
        tool_msg = result["messages"][2]
        assert tool_msg["content"] == "I'll check the weather for you."
        assert len(tool_msg["tool_calls"]) == 1
        assert tool_msg["tool_calls"][0]["function"]["name"] == "get_weather"
        
        # Check tool response
        tool_response = result["messages"][3]
        assert tool_response["role"] == "tool"
        assert tool_response["tool_call_id"] == "call_weather"
        assert json.loads(tool_response["content"])["temperature"] == 72
        
        # Check multimodal message
        multimodal = result["messages"][5]
        assert isinstance(multimodal["content"], list)
        assert multimodal["content"][0]["text"] == "Here's a photo from outside:"
        assert multimodal["content"][1]["type"] == "image_url"
    
    def test_unicode_and_special_characters_bidirectional(self) -> None:
        """Test messages with unicode and special characters."""
        openai_msg = {
            "messages": [{
                "role": "user",
                "content": "Test émojis: 👋🌍 and unicode: Ñoño, 中文, العربية, and escapes: \"quotes\" \\backslash\\"
            }]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # Should preserve all special characters
        assert result["messages"][0]["content"] == openai_msg["messages"][0]["content"]
    
    def test_empty_edge_cases_bidirectional(self) -> None:
        """Test edge cases with empty content."""
        test_cases = [
            # Empty content
            {"messages": [{"role": "user", "content": ""}]},
            
            # Whitespace only
            {"messages": [{"role": "user", "content": "   \n\t  "}]},
            
            # Empty tool arguments
            {
                "messages": [{
                    "role": "assistant",
                    "content": "",
                    "tool_calls": [{
                        "id": "call_1",
                        "type": "function", 
                        "function": {"name": "test", "arguments": "{}"}
                    }]
                }]
            }
        ]
        
        for test_case in test_cases:
            result = self._convert_openai_to_anthropic_and_back(test_case)
            assert result["messages"][0]["content"] == test_case["messages"][0]["content"]
    
    def test_large_message_performance(self) -> None:
        """Test conversion performance with large messages."""
        import time
        
        # Create a large conversation
        large_conversation = {
            "messages": []
        }
        
        # Add many messages
        for i in range(100):
            large_conversation["messages"].extend([
                {"role": "user", "content": f"Question {i}: " + "x" * 1000},
                {"role": "assistant", "content": f"Answer {i}: " + "y" * 1000}
            ])
        
        start_time = time.time()
        result = self._convert_openai_to_anthropic_and_back(large_conversation)
        elapsed = time.time() - start_time
        
        # Should complete in reasonable time
        assert elapsed < 1.0  # Should process 200 messages in under 1 second
        
        # Should preserve all messages
        assert len(result["messages"]) == 200
        assert result["messages"][0]["content"].startswith("Question 0:")
        assert result["messages"][-1]["content"].startswith("Answer 99:")
    
    def test_anthropic_to_openai_bidirectional(self) -> None:
        """Test Anthropic → OpenAI → Anthropic conversion."""
        anthropic_msg = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Analyze this:"},
                        {
                            "type": "image",
                            "source": {
                                "type": "url",
                                "url": "https://example.com/chart.png"
                            }
                        }
                    ]
                }
            ],
            "system": "You are a data analyst.",
            "tools": [{
                "type": "function",
                "function": {
                    "name": "calculate",
                    "description": "Perform calculations",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {"type": "string"}
                        }
                    }
                }
            }]
        }
        
        result = self._convert_anthropic_to_openai_and_back(anthropic_msg)
        
        # Should preserve system message
        assert result["system"] == "You are a data analyst."
        
        # Should preserve multimodal content
        assert len(result["messages"]) == 1
        assert len(result["messages"][0]["content"]) == 2
        assert result["messages"][0]["content"][0]["text"] == "Analyze this:"
        assert result["messages"][0]["content"][1]["type"] == "image"
        assert result["messages"][0]["content"][1]["source"]["url"] == "https://example.com/chart.png"
        
        # Should preserve tools
        assert len(result["tools"]) == 1
        assert result["tools"][0]["function"]["name"] == "calculate"
    
    def test_nested_content_structures_bidirectional(self) -> None:
        """Test deeply nested content structures."""
        openai_msg = {
            "messages": [{
                "role": "assistant",
                "content": "Processing multiple items:",
                "tool_calls": [
                    {
                        "id": "call_1",
                        "type": "function",
                        "function": {
                            "name": "process_batch",
                            "arguments": json.dumps({
                                "items": [
                                    {"id": 1, "data": {"nested": {"deep": {"value": "test1"}}}},
                                    {"id": 2, "data": {"nested": {"deep": {"value": "test2"}}}},
                                ]
                            })
                        }
                    }
                ]
            }]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # Parse the nested arguments
        tool_call = result["messages"][0]["tool_calls"][0]
        args = json.loads(tool_call["function"]["arguments"])
        
        # Check nested structure is preserved
        assert len(args["items"]) == 2
        assert args["items"][0]["data"]["nested"]["deep"]["value"] == "test1"
        assert args["items"][1]["data"]["nested"]["deep"]["value"] == "test2"
    
    def test_message_role_edge_cases(self) -> None:
        """Test handling of non-standard message roles."""
        # OpenAI format technically only supports specific roles
        # But test that conversion handles edge cases gracefully
        openai_msg = {
            "messages": [
                {"role": "system", "content": "System prompt"},
                {"role": "user", "content": "User message"},
                {"role": "assistant", "content": "Assistant response"},
                {"role": "tool", "tool_call_id": "123", "content": "Tool output"},
            ]
        }
        
        result = self._convert_openai_to_anthropic_and_back(openai_msg)
        
        # All standard roles should be preserved
        assert len(result["messages"]) == 4
        assert result["messages"][0]["role"] == "system"
        assert result["messages"][1]["role"] == "user"
        assert result["messages"][2]["role"] == "assistant"
        assert result["messages"][3]["role"] == "tool"
        assert result["messages"][3]["tool_call_id"] == "123"