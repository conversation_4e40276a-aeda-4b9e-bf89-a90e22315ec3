"""Test circuit breaker functionality in GrokClient."""
import threading
import time
from unittest.mock import patch

import httpx
import pytest

from llm_grok.client import GrokClient
from llm_grok.exceptions import NetworkError


class TestCircuitBreaker:
    """Test circuit breaker pattern implementation."""
    
    client: GrokClient
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.client = GrokClient(api_key="test-key")
        # Reset circuit breaker state
        self.client._consecutive_failures = 0
        self.client._circuit_opened_at = None
        self.client._half_open_requests = 0
    
    def test_circuit_breaker_initial_state(self) -> None:
        """Test circuit breaker starts in CLOSED state."""
        assert self.client._consecutive_failures == 0
        assert self.client._circuit_opened_at is None
        assert self.client._half_open_requests == 0
    
    def test_record_success_resets_failures(self) -> None:
        """Test successful request resets failure count."""
        # Set up some failures
        self.client._consecutive_failures = 3
        self.client._circuit_opened_at = 123456789  # Simulate open circuit
        self.client._half_open_requests = 1
        
        # Record success
        self.client._record_success()
        
        assert self.client._consecutive_failures == 0
        assert self.client._half_open_requests == 0
    
    def test_record_failure_increments_count(self) -> None:
        """Test failed request increments failure count."""
        initial_failures = self.client._consecutive_failures
        
        self.client._record_failure()
        
        assert self.client._consecutive_failures == initial_failures + 1
    
    def test_circuit_opens_after_threshold(self) -> None:
        """Test circuit opens after reaching failure threshold."""
        # Record failures up to threshold
        for _ in range(self.client.CIRCUIT_BREAKER_FAILURE_THRESHOLD):
            self.client._record_failure()
        
        assert self.client._circuit_opened_at is not None
        assert self.client._consecutive_failures == self.client.CIRCUIT_BREAKER_FAILURE_THRESHOLD
    
    def test_check_circuit_breaker_when_open(self) -> None:
        """Test circuit breaker rejects requests when OPEN."""
        # Open the circuit
        import time
        self.client._circuit_opened_at = time.time()
        
        with pytest.raises(NetworkError) as exc_info:
            self.client._check_circuit_breaker()
        
        assert "Circuit breaker is open" in str(exc_info.value)
    
    def test_circuit_transitions_to_half_open(self) -> None:
        """Test circuit transitions to HALF_OPEN after recovery time."""
        # Open the circuit with old timestamp
        import time
        self.client._circuit_opened_at = time.time() - self.client.CIRCUIT_BREAKER_RECOVERY_TIMEOUT - 1
        
        # Should not raise and should allow the request
        self.client._check_circuit_breaker()
        # Circuit is still technically "open" but allows test requests
    
    def test_half_open_allows_single_request(self) -> None:
        """Test HALF_OPEN state allows one request to test recovery."""
        import time
        # Set circuit as open but past recovery time
        self.client._circuit_opened_at = time.time() - self.client.CIRCUIT_BREAKER_RECOVERY_TIMEOUT - 1
        self.client._half_open_requests = 0
        
        # First check should pass
        self.client._check_circuit_breaker()
        
        # Half open request count should increment
        assert self.client._half_open_requests == 1
    
    def test_half_open_success_closes_circuit(self) -> None:
        """Test successful request in HALF_OPEN state closes circuit."""
        self.client._circuit_opened_at = 123456789  # Circuit was open
        self.client._consecutive_failures = 3
        self.client._half_open_requests = 1
        
        self.client._record_success()
        
        assert self.client._circuit_opened_at is None  # Circuit closed
        assert self.client._consecutive_failures == 0
        assert self.client._half_open_requests == 0
    
    def test_half_open_failure_reopens_circuit(self) -> None:
        """Test failed request in HALF_OPEN state reopens circuit."""
        import time
        # Simulate half-open state
        self.client._circuit_opened_at = time.time() - 10  # Was open
        self.client._half_open_requests = 1
        
        self.client._record_failure()
        
        # Circuit should remain open with updated timestamp
        assert self.client._circuit_opened_at is not None
        assert self.client._circuit_opened_at > time.time() - 1  # Recently updated
    
    def test_streaming_with_circuit_breaker_success(self) -> None:
        """Test streaming request records success through circuit breaker."""
        from unittest.mock import Mock, MagicMock
        
        mock_response = Mock()
        mock_response.iter_lines = Mock(return_value=iter([
            'data: {"choices": [{"delta": {"content": "test"}}]}',
            'data: [DONE]'
        ]))
        
        mock_stream_cm = MagicMock()
        mock_stream_cm.__enter__ = Mock(return_value=mock_response)
        mock_stream_cm.__exit__ = Mock(return_value=None)
        
        # Use the wrapper
        wrapped = self.client._wrap_stream_with_circuit_breaker(mock_stream_cm)
        
        # Process the stream
        with wrapped as response:
            chunks = []
            for line in response.iter_lines():
                chunks.append(line)
        
        # Should have recorded success
        assert self.client._consecutive_failures == 0
    
    def test_streaming_with_circuit_breaker_failure(self) -> None:
        """Test streaming request records failure through circuit breaker."""
        from unittest.mock import Mock, MagicMock
        
        mock_stream_cm = MagicMock()
        mock_stream_cm.__enter__ = Mock(side_effect=httpx.HTTPError("Connection failed"))
        
        # Use the wrapper
        wrapped = self.client._wrap_stream_with_circuit_breaker(mock_stream_cm)
        
        # Process should raise and record failure
        with pytest.raises(httpx.HTTPError):
            with wrapped as response:
                pass
        
        # Should have recorded failure
        assert self.client._consecutive_failures == 1
    
    def test_streaming_exception_during_iteration(self) -> None:
        """Test exception during stream iteration records failure."""
        from unittest.mock import Mock, MagicMock
        
        mock_response = Mock()
        # Simulate error during iteration
        mock_response.iter_lines = Mock(side_effect=httpx.HTTPError("Stream interrupted"))
        
        mock_stream_cm = MagicMock()
        mock_stream_cm.__enter__ = Mock(return_value=mock_response)
        mock_stream_cm.__exit__ = Mock(return_value=None)
        
        wrapped = self.client._wrap_stream_with_circuit_breaker(mock_stream_cm)
        
        with pytest.raises(httpx.HTTPError):
            with wrapped as response:
                for _ in response.iter_lines():
                    pass
        
        # Should have recorded failure
        assert self.client._consecutive_failures == 1
    
    def test_thread_safety_concurrent_failures(self) -> None:
        """Test thread safety of circuit breaker state updates."""
        def record_failures() -> None:
            for _ in range(2):
                self.client._record_failure()
        
        # Create multiple threads
        threads = [threading.Thread(target=record_failures) for _ in range(3)]
        
        # Start all threads
        for t in threads:
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        # Should have recorded all failures (6 total)
        assert self.client._consecutive_failures == 6
        assert self.client._circuit_opened_at is not None  # Circuit opened
    
    def test_circuit_breaker_with_rate_limit_error(self) -> None:
        """Test circuit breaker behavior with rate limit errors."""
        # Rate limit errors should not trigger circuit breaker
        initial_failures = self.client._consecutive_failures
        
        # Simulate rate limit error - should not affect circuit breaker
        # (This would be handled differently in actual implementation)
        assert self.client._consecutive_failures == initial_failures
    
    def test_circuit_breaker_recovery_time_calculation(self) -> None:
        """Test circuit breaker recovery time is respected."""
        import time
        # Open circuit with specific timestamp
        test_time = time.time()
        self.client._circuit_opened_at = test_time
        
        # Just before recovery time - should still be OPEN
        with patch('time.time', return_value=test_time + self.client.CIRCUIT_BREAKER_RECOVERY_TIMEOUT - 1):
            with pytest.raises(NetworkError):
                self.client._check_circuit_breaker()
        
        # After recovery time - should allow request
        with patch('time.time', return_value=test_time + self.client.CIRCUIT_BREAKER_RECOVERY_TIMEOUT + 1):
            self.client._check_circuit_breaker()
            assert self.client._half_open_requests == 1
    
    def test_circuit_breaker_state_transitions(self) -> None:
        """Test complete circuit breaker state transition cycle."""
        import time
        # Start CLOSED (no circuit opened timestamp)
        assert self.client._circuit_opened_at is None
        
        # Fail enough times to OPEN
        for _ in range(self.client.CIRCUIT_BREAKER_FAILURE_THRESHOLD):
            self.client._record_failure()
        assert self.client._circuit_opened_at is not None  # Circuit opened
        
        # Wait for recovery time and check - allows half-open request
        self.client._circuit_opened_at = time.time() - self.client.CIRCUIT_BREAKER_RECOVERY_TIMEOUT - 1
        self.client._check_circuit_breaker()
        assert self.client._half_open_requests == 1
        
        # Success in HALF_OPEN - back to CLOSED
        self.client._record_success()
        assert self.client._circuit_opened_at is None  # Circuit closed
        assert self.client._consecutive_failures == 0
        assert self.client._half_open_requests == 0