"""Advanced SSRF protection tests for llm-grok."""
import socket
from unittest.mock import patch, Mock

import pytest

from llm_grok.formats.openai import OpenAIFormatHandler
from llm_grok.exceptions import ValidationError


class TestAdvancedSSRF:
    """Test advanced SSRF attack scenarios."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.handler = OpenAIFormatHandler(model_id="test-model")
    
    def test_dns_rebinding_protection(self) -> None:
        """Test protection against DNS rebinding attacks."""
        # Mock socket.gethostbyname to simulate DNS rebinding
        with patch('socket.gethostbyname') as mock_gethostbyname:
            # First resolution returns public IP
            mock_gethostbyname.return_value = "*******"
            
            # URL validation should check DNS resolution
            with patch('urllib.parse.urlparse') as mock_urlparse:
                mock_parsed = Mock()
                mock_parsed.scheme = "https"
                mock_parsed.hostname = "evil.com"
                mock_parsed.port = None
                mock_urlparse.return_value = mock_parsed
                
                # Simulate DNS changing to localhost after initial check
                mock_gethostbyname.side_effect = ["*******", "127.0.0.1"]
                
                # Should validate successfully on first check
                result = self.handler.validate_image_url("https://evil.com/image.jpg")
                assert result == "https://evil.com/image.jpg"
    
    def test_url_shortener_redirect_protection(self) -> None:
        """Test protection against URL shorteners and redirects."""
        # Common URL shorteners should be blocked
        shorteners = [
            "https://bit.ly/malicious",
            "https://tinyurl.com/evil",
            "https://goo.gl/bad",
            "https://t.co/twitter",
            "https://short.link/test",
            "https://rb.gy/test",
            "https://cutt.ly/test"
        ]
        
        for url in shorteners:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "URL shorteners are not allowed" in str(exc_info.value) or "suspicious" in str(exc_info.value).lower()
    
    def test_ipv6_edge_cases(self) -> None:
        """Test IPv6 address edge cases."""
        # Various IPv6 localhost representations
        ipv6_localhost_variants = [
            "https://[::1]/image.jpg",
            "https://[0:0:0:0:0:0:0:1]/image.jpg",
            "https://[::ffff:127.0.0.1]/image.jpg",  # IPv4-mapped IPv6
            "https://[::ffff:7f00:1]/image.jpg",     # IPv4-mapped hex
            "https://[0000:0000:0000:0000:0000:0000:0000:0001]/image.jpg",
            "https://[fe80::1]/image.jpg",           # Link-local
            "https://[fc00::1]/image.jpg",           # Unique local
            "https://[fd00::1]/image.jpg",           # Unique local
        ]
        
        for url in ipv6_localhost_variants:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "not allowed" in str(exc_info.value) or "private" in str(exc_info.value).lower()
    
    def test_url_parsing_bypass_attempts(self) -> None:
        """Test various URL parsing bypass attempts."""
        bypass_attempts = [
            # Double @ attempts
            "https://example.com@127.0.0.1/image.jpg",
            "https://user@127.0.0.1:<EMAIL>/image.jpg",
            
            # Encoded characters
            "https://127.0.0.%31/image.jpg",  # %31 = 1
            "https://localhost%00.example.com/image.jpg",  # Null byte
            "https://127.0.0.1%2F/image.jpg",  # Encoded slash
            
            # Alternative representations
            "https://2130706433/image.jpg",  # Decimal IP for 127.0.0.1
            "https://0x7f000001/image.jpg",  # Hex IP for 127.0.0.1
            "https://0177.0.0.1/image.jpg",  # Octal representation
            
            # Unicode tricks
            "https://１２７.０.０.１/image.jpg",  # Full-width numbers
            "https://⑫⑦.⓪.⓪.①/image.jpg",  # Circled numbers
            
            # Case variations and typos that might be normalized
            "https://LocalHost/image.jpg",
            "https://LOCALHOST/image.jpg",
            "https://LoCaLhOsT/image.jpg",
        ]
        
        for url in bypass_attempts:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "not allowed" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()
    
    def test_cloud_metadata_endpoints(self) -> None:
        """Test blocking of cloud provider metadata endpoints."""
        metadata_urls = [
            # AWS
            "http://***************/latest/meta-data/",
            "http://***************/latest/user-data/",
            "http://metadata.aws/",
            
            # GCP
            "http://metadata.google.internal/",
            "http://***************/computeMetadata/v1/",
            
            # Azure
            "http://***************/metadata/instance",
            
            # DigitalOcean
            "http://***************/metadata/v1/",
            
            # Oracle Cloud
            "http://***************/opc/v1/instance/",
        ]
        
        for url in metadata_urls:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "169.254" in str(exc_info.value) or "metadata" in str(exc_info.value).lower()
    
    def test_international_domain_names(self) -> None:
        """Test handling of international domain names (IDN)."""
        idn_urls = [
            # Homograph attacks
            "https://gооgle.com/image.jpg",  # Cyrillic 'o's
            "https://аррӏе.com/image.jpg",   # Mixed scripts
            "https://xn--e1awd7f.com/image.jpg",  # Punycode
            
            # Valid IDN that should work
            "https://münchen.de/image.jpg",
            "https://日本.jp/image.jpg",
            "https://xn--mnchen-3ya.de/image.jpg",  # Punycode for münchen
        ]
        
        # First three should fail or be suspicious
        for url in idn_urls[:3]:
            # Some might pass validation but should be handled carefully
            try:
                result = self.handler.validate_image_url(url)
                # If it passes, it should at least normalize to punycode
                assert "xn--" in result or url == result
            except ValidationError:
                # This is also acceptable
                pass
        
        # Last three should generally work (valid IDN)
        for url in idn_urls[3:]:
            try:
                result = self.handler.validate_image_url(url)
                assert isinstance(result, str)
            except ValidationError as e:
                # Some implementations might be more restrictive
                assert "invalid" not in str(e).lower()
    
    def test_toctou_race_condition_awareness(self) -> None:
        """Test awareness of time-of-check vs time-of-use issues."""
        # This is more of a documentation test
        # Real TOCTOU protection would require implementation changes
        
        # Simulate a URL that passes validation
        safe_url = "https://example.com/image.jpg"
        
        # First validation passes
        result1 = self.handler.validate_image_url(safe_url)
        assert result1 == safe_url
        
        # In real scenario, DNS could change between validation and use
        # This test documents the limitation
        with patch('socket.gethostbyname') as mock_gethostbyname:
            # DNS now resolves to localhost
            mock_gethostbyname.return_value = "127.0.0.1"
            
            # Validation would still pass because it doesn't re-check
            # This documents that the application should:
            # 1. Validate immediately before use
            # 2. Use the validated URL, not the original
            # 3. Consider using a allowlist of domains
            result2 = self.handler.validate_image_url(safe_url)
            assert result2 == safe_url
    
    def test_performance_with_many_urls(self) -> None:
        """Test validation performance doesn't degrade with many URLs."""
        import time
        
        # Generate many URLs to validate
        urls = [f"https://example{i}.com/image{i}.jpg" for i in range(100)]
        
        start_time = time.time()
        
        for url in urls:
            try:
                self.handler.validate_image_url(url)
            except ValidationError:
                pass
        
        elapsed_time = time.time() - start_time
        
        # Should process 100 URLs in under 1 second
        assert elapsed_time < 1.0, f"Validation too slow: {elapsed_time:.2f}s for 100 URLs"
    
    def test_protocol_confusion_attacks(self) -> None:
        """Test protection against protocol confusion."""
        protocol_attacks = [
            # File protocol
            "file:///etc/passwd",
            "file://localhost/etc/passwd",
            
            # FTP protocol
            "ftp://127.0.0.1/image.jpg",
            "ftps://localhost/image.jpg",
            
            # Other protocols
            "gopher://localhost/",
            "dict://localhost/",
            "sftp://localhost/",
            "ldap://localhost/",
            "telnet://localhost/",
            
            # Javascript protocol (XSS)
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            
            # Mixed case attempts
            "FiLe:///etc/passwd",
            "HTTP://localhost/image.jpg",
        ]
        
        for url in protocol_attacks:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "scheme" in str(exc_info.value).lower() or "not allowed" in str(exc_info.value).lower()
    
    def test_port_scanning_protection(self) -> None:
        """Test protection against port scanning via SSRF."""
        # Common internal service ports
        sensitive_ports = [
            22,    # SSH
            23,    # Telnet
            25,    # SMTP
            110,   # POP3
            135,   # Windows RPC
            139,   # NetBIOS
            445,   # SMB
            1433,  # MSSQL
            3306,  # MySQL
            3389,  # RDP
            5432,  # PostgreSQL
            5900,  # VNC
            6379,  # Redis
            8080,  # Alt HTTP
            9200,  # Elasticsearch
            11211, # Memcached
            27017, # MongoDB
        ]
        
        for port in sensitive_ports:
            url = f"https://localhost:{port}/image.jpg"
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "localhost" in str(exc_info.value) or str(port) in str(exc_info.value)
    
    def test_subdomain_bypass_attempts(self) -> None:
        """Test subdomain-based bypass attempts."""
        subdomain_attacks = [
            # Subdomain tricks
            "https://localhost.example.com/image.jpg",
            "https://127.0.0.1.example.com/image.jpg",
            "https://example.com.localhost/image.jpg",
            "https://localhost-example.com/image.jpg",
            
            # Wildcard DNS that might resolve to localhost
            "https://127.0.0.1.nip.io/image.jpg",
            "https://localtest.me/image.jpg",
            "https://lvh.me/image.jpg",
            
            # Zero-width characters (if not properly handled)
            "https://local\u200bhost/image.jpg",
            "https://local\ufeffhost/image.jpg",
        ]
        
        for url in subdomain_attacks:
            with pytest.raises(ValidationError) as exc_info:
                self.handler.validate_image_url(url)
            assert "not allowed" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()