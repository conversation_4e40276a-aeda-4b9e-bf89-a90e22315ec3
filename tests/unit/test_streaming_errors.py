"""Test streaming error scenarios and edge cases."""
import json
from unittest.mock import Mo<PERSON>, patch
from typing import Iterator, List

import pytest
import httpx

from llm_grok.processors.streaming import StreamProcessor
from llm_grok.exceptions import GrokError, ValidationError


class TestStreamingErrorScenarios:
    """Test error handling in streaming responses."""
    
    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.processor = StreamProcessor()
    
    def test_malformed_json_chunks(self) -> None:
        """Test handling of malformed JSON in stream."""
        malformed_chunks = [
            'data: {"choices": [{"delta": {"content": "test"',  # Incomplete JSON
            'data: {"choices": [{"delta": {"content": "test"}}}',  # Missing closing ]
            'data: {invalid json}',  # Invalid JSON
            'data: {"choices": [{"delta": null}]}',  # Null delta
            'data: {"choices": []}',  # Empty choices
            'data: {}',  # Missing choices
            'data: [DONE]'
        ]
        
        accumulated_content = ""
        errors_caught = 0
        
        for chunk in malformed_chunks:
            try:
                result = self.processor.process_stream_delta(chunk)
                if result:
                    accumulated_content += result.get("content", "")
            except (json.JSONDecodeError, ValidationError, KeyError):
                errors_caught += 1
        
        # Should handle some errors gracefully
        assert errors_caught >= 3  # At least the clearly malformed ones
    
    def test_buffer_overflow_protection(self) -> None:
        """Test protection against buffer overflow in streaming."""
        # Create a large content chunk
        large_content = "x" * (1024 * 1024)  # 1MB chunk
        
        # Test single large chunk
        large_chunk = {
            "choices": [{"delta": {"content": large_content}}]
        }
        
        # Should process but with size awareness
        chunk_str = f"data: {json.dumps(large_chunk)}"
        
        # Test that buffer limits are enforced
        self.processor._buffer = bytearray()
        
        # Simulate receiving data
        for i in range(0, len(chunk_str), 1024):
            chunk_part = chunk_str[i:i+1024].encode()
            self.processor._buffer.extend(chunk_part)
            
            # Check buffer size
            if len(self.processor._buffer) > self.processor.MAX_BUFFER_SIZE:
                with pytest.raises(ValueError) as exc_info:
                    self.processor._check_buffer_size()
                assert "Buffer size exceeded" in str(exc_info.value)
                break
    
    def test_connection_interruption_recovery(self) -> None:
        """Test handling of connection interruptions during streaming."""
        chunks = [
            'data: {"choices": [{"delta": {"content": "Hello"}}]}',
            'data: {"choices": [{"delta": {"content": " world"}}]}',
            # Simulated interruption - incomplete data
            'data: {"choices": [{"delta": {"con',
            # Recovery might not be possible, should handle gracefully
        ]
        
        content_parts = []
        
        for chunk in chunks[:-1]:  # All but the incomplete one
            try:
                result = self.processor.process_stream_delta(chunk)
                if result and result.get("content"):
                    content_parts.append(result["content"])
            except Exception:
                pass
        
        # Should have processed the complete chunks
        assert content_parts == ["Hello", " world"]
        
        # The incomplete chunk should raise an error
        with pytest.raises((json.JSONDecodeError, ValidationError)):
            self.processor.process_stream_delta(chunks[-1])
    
    def test_out_of_order_tool_calls(self) -> None:
        """Test handling of out-of-order tool call chunks."""
        # Tool calls arriving out of sequence
        chunks = [
            {
                "choices": [{
                    "delta": {
                        "tool_calls": [{
                            "index": 1,  # Second tool call arrives first
                            "id": "call_2",
                            "function": {"name": "tool2", "arguments": '{"arg": '}
                        }]
                    }
                }]
            },
            {
                "choices": [{
                    "delta": {
                        "tool_calls": [{
                            "index": 0,  # First tool call arrives second
                            "id": "call_1", 
                            "function": {"name": "tool1", "arguments": '{"param": "value"}'}
                        }]
                    }
                }]
            },
            {
                "choices": [{
                    "delta": {
                        "tool_calls": [{
                            "index": 1,  # Continue second tool call
                            "function": {"arguments": '"data"}'}
                        }]
                    }
                }]
            }
        ]
        
        # Process chunks
        for chunk in chunks:
            chunk_str = f"data: {json.dumps(chunk)}"
            self.processor.process_stream_delta(chunk_str)
        
        # Finalize tool calls
        finalized = self.processor.finalize_tool_calls()
        
        # Should have both tool calls in correct order
        assert len(finalized) == 2
        assert finalized[0]["id"] == "call_1"
        assert finalized[0]["function"]["name"] == "tool1"
        assert finalized[1]["id"] == "call_2"
        assert finalized[1]["function"]["name"] == "tool2"
    
    def test_mixed_content_and_tool_calls(self) -> None:
        """Test complex interleaving of content and tool calls."""
        chunks = [
            'data: {"choices": [{"delta": {"content": "Let me help you with that. "}}]}',
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "id": "call_1", "function": {"name": "search", "arguments": "{\\"query\\": \\""}}]}}]}',
            'data: {"choices": [{"delta": {"content": "I\'ll search for "}}]}',
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "function": {"arguments": "test query\\"}"}}]}}]}',
            'data: {"choices": [{"delta": {"content": "the information."}}]}',
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 1, "id": "call_2", "function": {"name": "calculate", "arguments": "{\\"num\\": 42}"}}]}}]}',
        ]
        
        contents = []
        
        for chunk in chunks:
            result = self.processor.process_stream_delta(chunk)
            if result and result.get("content"):
                contents.append(result["content"])
        
        # Should have collected all content
        full_content = "".join(contents)
        assert "Let me help you with that." in full_content
        assert "I'll search for" in full_content
        assert "the information." in full_content
        
        # Should have both tool calls
        tool_calls = self.processor.finalize_tool_calls()
        assert len(tool_calls) == 2
        assert tool_calls[0]["function"]["name"] == "search"
        assert tool_calls[1]["function"]["name"] == "calculate"
    
    def test_special_characters_in_tool_arguments(self) -> None:
        """Test handling of special characters in tool call arguments."""
        special_cases = [
            '{"text": "Hello\\nWorld"}',  # Newlines
            '{"path": "C:\\\\Users\\\\<USER>\\"quoted\\" text"}',  # Escaped quotes
            '{"json": "{\\"nested\\": true}"}',  # Nested JSON
        ]
        
        for i, args in enumerate(special_cases):
            chunk = {
                "choices": [{
                    "delta": {
                        "tool_calls": [{
                            "index": i,
                            "id": f"call_{i}",
                            "function": {"name": f"tool_{i}", "arguments": args}
                        }]
                    }
                }]
            }
            
            chunk_str = f"data: {json.dumps(chunk)}"
            self.processor.process_stream_delta(chunk_str)
        
        # All tool calls should be processed correctly
        tool_calls = self.processor.finalize_tool_calls()
        assert len(tool_calls) == len(special_cases)
        
        # Verify arguments are properly parsed
        for i, tc in enumerate(tool_calls):
            args = json.loads(tc["function"]["arguments"])
            assert isinstance(args, dict)
    
    def test_extremely_long_tool_arguments(self) -> None:
        """Test handling of very long tool call arguments split across chunks."""
        # Create a large argument that will be split
        large_data = {"items": ["item_" + str(i) for i in range(1000)]}
        large_json = json.dumps(large_data)
        
        # Split into chunks
        chunk_size = 100
        chunks = []
        
        # First chunk with tool call start
        chunks.append({
            "choices": [{
                "delta": {
                    "tool_calls": [{
                        "index": 0,
                        "id": "call_large",
                        "function": {"name": "process_data", "arguments": large_json[:chunk_size]}
                    }]
                }
            }]
        })
        
        # Middle chunks with continued arguments
        for i in range(chunk_size, len(large_json), chunk_size):
            chunks.append({
                "choices": [{
                    "delta": {
                        "tool_calls": [{
                            "index": 0,
                            "function": {"arguments": large_json[i:i+chunk_size]}
                        }]
                    }
                }]
            })
        
        # Process all chunks
        for chunk in chunks:
            chunk_str = f"data: {json.dumps(chunk)}"
            self.processor.process_stream_delta(chunk_str)
        
        # Verify the complete argument was accumulated
        tool_calls = self.processor.finalize_tool_calls()
        assert len(tool_calls) == 1
        
        parsed_args = json.loads(tool_calls[0]["function"]["arguments"])
        assert len(parsed_args["items"]) == 1000
        assert parsed_args["items"][0] == "item_0"
        assert parsed_args["items"][-1] == "item_999"
    
    def test_empty_and_whitespace_chunks(self) -> None:
        """Test handling of empty and whitespace-only chunks."""
        chunks = [
            'data: {"choices": [{"delta": {"content": ""}}]}',  # Empty content
            'data: {"choices": [{"delta": {"content": "   "}}]}',  # Whitespace
            'data: {"choices": [{"delta": {"content": "\\n\\n"}}]}',  # Newlines
            'data: {"choices": [{"delta": {"content": "\\t"}}]}',  # Tab
            'data: {"choices": [{"delta": {"content": "Hello"}}]}',  # Real content
            'data: {"choices": [{"delta": {}}]}',  # Empty delta
            'data: {"choices": [{"delta": {"content": null}}]}',  # Null content
        ]
        
        contents = []
        
        for chunk in chunks:
            try:
                result = self.processor.process_stream_delta(chunk)
                if result and result.get("content") is not None:
                    contents.append(result["content"])
            except Exception:
                pass
        
        # Should preserve whitespace but handle null/empty gracefully
        assert "" in contents  # Empty string
        assert "   " in contents  # Whitespace
        assert "\\n\\n" in contents  # Newlines
        assert "\\t" in contents  # Tab
        assert "Hello" in contents  # Real content
    
    def test_concurrent_stream_processing(self) -> None:
        """Test thread safety of stream processing."""
        import threading
        import time
        
        # Shared processor (not recommended in practice)
        processor = StreamProcessor()
        results = []
        errors = []
        
        def process_stream(stream_id: int, chunks: List[str]) -> None:
            try:
                for chunk in chunks:
                    result = processor.process_stream_delta(chunk)
                    if result:
                        results.append((stream_id, result))
                    time.sleep(0.001)  # Simulate network delay
            except Exception as e:
                errors.append((stream_id, str(e)))
        
        # Create concurrent streams
        stream1_chunks = [
            'data: {"choices": [{"delta": {"content": "Stream 1: "}}]}',
            'data: {"choices": [{"delta": {"content": "Hello"}}]}',
        ]
        
        stream2_chunks = [
            'data: {"choices": [{"delta": {"content": "Stream 2: "}}]}',
            'data: {"choices": [{"delta": {"content": "World"}}]}',
        ]
        
        # Start threads
        thread1 = threading.Thread(target=process_stream, args=(1, stream1_chunks))
        thread2 = threading.Thread(target=process_stream, args=(2, stream2_chunks))
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        # Should have results from both streams (order may vary)
        assert len(results) >= 2  # At least some results
        stream_ids = set(r[0] for r in results)
        assert 1 in stream_ids or 2 in stream_ids  # At least one stream processed
    
    def test_error_recovery_in_tool_calls(self) -> None:
        """Test error recovery when tool calls are malformed."""
        chunks = [
            # Valid tool call start
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "id": "call_1", "function": {"name": "tool1", "arguments": "{"}}]}}]}',
            # Malformed arguments
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 0, "function": {"arguments": "invalid json"}}]}}]}',
            # Try to continue with new tool call
            'data: {"choices": [{"delta": {"tool_calls": [{"index": 1, "id": "call_2", "function": {"name": "tool2", "arguments": "{\\"valid\\": true}"}}]}}]}',
        ]
        
        for chunk in chunks:
            try:
                self.processor.process_stream_delta(chunk)
            except Exception:
                pass  # Continue processing
        
        # Should be able to finalize what's valid
        tool_calls = self.processor.finalize_tool_calls()
        
        # Second tool call should be valid
        valid_calls = [tc for tc in tool_calls if tc.get("id") == "call_2"]
        assert len(valid_calls) == 1
        assert json.loads(valid_calls[0]["function"]["arguments"])["valid"] is True